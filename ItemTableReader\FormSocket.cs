using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormSocket : Form
{
	private static ItemSocket tempSocket = new ItemSocket();

	private IContainer components;

	private ListBox listBoxSockets;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private Button btnIcon;

	private TableLayoutPanel tableLayoutPanel1;

	private Label label1;

	private Label label2;

	private Label label3;

	private Label label4;

	private Label label5;

	private Label label6;

	private Label label7;

	private Label label8;

	private Label label9;

	private Label label10;

	private TextBox textBoxID;

	private TextBox textBoxType;

	private TextBox textBoxSecondType;

	private TextBox textBoxModelIndex;

	private TextBox textBoxIconIndex;

	private TextBox textBoxItemRank;

	private TextBox textBoxGrade;

	private TextBox textBoxName;

	private TextBox textBoxDescription;

	private TextBox textBoxPrice;

	private GroupBox groupBoxSkill1;

	private GroupBox groupBoxSkill2;

	private GroupBox groupBoxSkill3;

	private GroupBox groupBoxSkill4;

	private Label labelSkillID1;

	private Label labelSkillDesc1;

	private Label labelType1;

	private Label labelValue1;

	private Label labelProb1;

	private TextBox textBoxSkillID1;

	private TextBox textBoxSkillDesc1;

	private TextBox textBoxType1;

	private TextBox textBoxValue1;

	private TextBox textBoxProb1;

	private Label labelSkillID2;

	private Label labelSkillDesc2;

	private Label labelType2;

	private Label labelValue2;

	private Label labelProb2;

	private TextBox textBoxSkillID2;

	private TextBox textBoxSkillDesc2;

	private TextBox textBoxType2;

	private TextBox textBoxValue2;

	private TextBox textBoxProb2;

	private Label labelSkillID3;

	private Label labelSkillDesc3;

	private Label labelType3;

	private Label labelValue3;

	private Label labelProb3;

	private TextBox textBoxSkillID3;

	private TextBox textBoxSkillDesc3;

	private TextBox textBoxType3;

	private TextBox textBoxValue3;

	private TextBox textBoxProb3;

	private Label labelSkillID4;

	private Label labelSkillDesc4;

	private Label labelType4;

	private Label labelValue4;

	private Label labelProb4;

	private TextBox textBoxSkillID4;

	private TextBox textBoxSkillDesc4;

	private TextBox textBoxType4;

	private TextBox textBoxValue4;

	private TextBox textBoxProb4;

	private CheckBox checkBoxBlockDrop;

	private CheckBox checkBoxBlockTrade;

	private CheckBox checkBoxBlockNPC;

	private CheckBox checkBoxBlockStorage;

	public FormSocket()
	{
		InitializeComponent();
		LoadSocketList();
	}

	private void LoadSocketList()
	{
		listBoxSockets.Items.Clear();
		foreach (ItemSocket socket in ItemSocket.Sockets.Values.OrderBy(x => x.ID))
		{
			listBoxSockets.Items.Add($"[{socket.ID}] {socket.Name ?? "Unknown Socket"}");
		}
	}

	private ItemSocket GetSelectedSocket()
	{
		if (listBoxSockets.SelectedItem is string selectedText)
		{
			// Extract ID from the format "[ID] Name"
			int startIndex = selectedText.IndexOf('[') + 1;
			int endIndex = selectedText.IndexOf(']');
			if (startIndex > 0 && endIndex > startIndex)
			{
				string idText = selectedText.Substring(startIndex, endIndex - startIndex);
				if (short.TryParse(idText, out short socketId))
				{
					if (ItemSocket.Sockets.ContainsKey(socketId))
					{
						return ItemSocket.Sockets[socketId];
					}
				}
			}
		}
		return null;
	}

	private void listBoxSockets_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemSocket socket = GetSelectedSocket();
		if (socket != null)
		{
			LoadSocketData(socket);
		}
	}

	private void LoadSocketData(ItemSocket socket)
	{
		textBoxID.Text = socket.ID.ToString();
		textBoxType.Text = socket.Type.ToString();
		textBoxSecondType.Text = socket.SecondType.ToString();
		textBoxModelIndex.Text = socket.ModelIndex.ToString();
		textBoxIconIndex.Text = socket.IconIndex.ToString();
		textBoxItemRank.Text = socket.ItemRank.ToString();
		textBoxGrade.Text = socket.Grade.ToString();
		textBoxName.Text = socket.Name ?? "";
		textBoxDescription.Text = socket.Description ?? "";
		textBoxPrice.Text = socket.Price.ToString();

		// Skill Effect 1
		textBoxSkillID1.Text = socket.skillEffectID1.ToString();
		textBoxSkillDesc1.Text = socket.skillDescID1.ToString();
		textBoxType1.Text = socket.type1.ToString();
		textBoxValue1.Text = socket.value1.ToString();
		textBoxProb1.Text = socket.prob1.ToString();

		// Skill Effect 2
		textBoxSkillID2.Text = socket.skillEffectID2.ToString();
		textBoxSkillDesc2.Text = socket.skillDescID2.ToString();
		textBoxType2.Text = socket.type2.ToString();
		textBoxValue2.Text = socket.value2.ToString();
		textBoxProb2.Text = socket.prob2.ToString();

		// Skill Effect 3
		textBoxSkillID3.Text = socket.skillEffectID3.ToString();
		textBoxSkillDesc3.Text = socket.skillDescID3.ToString();
		textBoxType3.Text = socket.type3.ToString();
		textBoxValue3.Text = socket.value3.ToString();
		textBoxProb3.Text = socket.prob3.ToString();

		// Skill Effect 4
		textBoxSkillID4.Text = socket.skillEffectID4.ToString();
		textBoxSkillDesc4.Text = socket.skillDescID4.ToString();
		textBoxType4.Text = socket.type4.ToString();
		textBoxValue4.Text = socket.value4.ToString();
		textBoxProb4.Text = socket.prob4.ToString();

		// Block settings
		checkBoxBlockDrop.Checked = socket.BlockDrop;
		checkBoxBlockTrade.Checked = socket.userTrade != 0;
		checkBoxBlockNPC.Checked = socket.BlockNpcSell;
		checkBoxBlockStorage.Checked = socket.BlockStorage;
	}

	private void SaveSocketData()
	{
		ItemSocket socket = GetSelectedSocket();
		if (socket != null)
		{
			if (short.TryParse(textBoxID.Text, out short id))
				socket.ID = id;
			if (sbyte.TryParse(textBoxType.Text, out sbyte type))
				socket.Type = type;
			if (sbyte.TryParse(textBoxSecondType.Text, out sbyte secondType))
				socket.SecondType = secondType;
			if (short.TryParse(textBoxModelIndex.Text, out short modelIndex))
				socket.ModelIndex = modelIndex;
			if (short.TryParse(textBoxIconIndex.Text, out short iconIndex))
				socket.IconIndex = iconIndex;
			if (ushort.TryParse(textBoxItemRank.Text, out ushort itemRank))
				socket.ItemRank = itemRank;
			if (byte.TryParse(textBoxGrade.Text, out byte grade))
				socket.Grade = grade;
			if (uint.TryParse(textBoxPrice.Text, out uint price))
				socket.Price = price;

			// Skill Effect 1
			if (short.TryParse(textBoxSkillID1.Text, out short skillID1))
				socket.skillEffectID1 = skillID1;
			if (ushort.TryParse(textBoxSkillDesc1.Text, out ushort skillDesc1))
				socket.skillDescID1 = skillDesc1;
			if (sbyte.TryParse(textBoxType1.Text, out sbyte type1))
				socket.type1 = type1;
			if (short.TryParse(textBoxValue1.Text, out short value1))
				socket.value1 = value1;
			if (sbyte.TryParse(textBoxProb1.Text, out sbyte prob1))
				socket.prob1 = prob1;

			// Skill Effect 2
			if (short.TryParse(textBoxSkillID2.Text, out short skillID2))
				socket.skillEffectID2 = skillID2;
			if (ushort.TryParse(textBoxSkillDesc2.Text, out ushort skillDesc2))
				socket.skillDescID2 = skillDesc2;
			if (sbyte.TryParse(textBoxType2.Text, out sbyte type2))
				socket.type2 = type2;
			if (short.TryParse(textBoxValue2.Text, out short value2))
				socket.value2 = value2;
			if (sbyte.TryParse(textBoxProb2.Text, out sbyte prob2))
				socket.prob2 = prob2;

			// Skill Effect 3
			if (short.TryParse(textBoxSkillID3.Text, out short skillID3))
				socket.skillEffectID3 = skillID3;
			if (ushort.TryParse(textBoxSkillDesc3.Text, out ushort skillDesc3))
				socket.skillDescID3 = skillDesc3;
			if (sbyte.TryParse(textBoxType3.Text, out sbyte type3))
				socket.type3 = type3;
			if (short.TryParse(textBoxValue3.Text, out short value3))
				socket.value3 = value3;
			if (sbyte.TryParse(textBoxProb3.Text, out sbyte prob3))
				socket.prob3 = prob3;

			// Skill Effect 4
			if (short.TryParse(textBoxSkillID4.Text, out short skillID4))
				socket.skillEffectID4 = skillID4;
			if (ushort.TryParse(textBoxSkillDesc4.Text, out ushort skillDesc4))
				socket.skillDescID4 = skillDesc4;
			if (sbyte.TryParse(textBoxType4.Text, out sbyte type4))
				socket.type4 = type4;
			if (short.TryParse(textBoxValue4.Text, out short value4))
				socket.value4 = value4;
			if (sbyte.TryParse(textBoxProb4.Text, out sbyte prob4))
				socket.prob4 = prob4;

			// Block settings
			socket.BlockDrop = checkBoxBlockDrop.Checked;
			socket.userTrade = (sbyte)(checkBoxBlockTrade.Checked ? 1 : 0);
			socket.BlockNpcSell = checkBoxBlockNPC.Checked;
			socket.BlockStorage = checkBoxBlockStorage.Checked;
		}
	}

	private void InitializeComponent()
	{
		this.listBoxSockets = new ListBox();
		this.tabControl1 = new TabControl();
		this.tabPage1 = new TabPage();
		this.tableLayoutPanel1 = new TableLayoutPanel();
		this.label1 = new Label();
		this.label2 = new Label();
		this.label3 = new Label();
		this.label4 = new Label();
		this.label5 = new Label();
		this.label6 = new Label();
		this.label7 = new Label();
		this.label8 = new Label();
		this.label9 = new Label();
		this.label10 = new Label();
		this.textBoxID = new TextBox();
		this.textBoxType = new TextBox();
		this.textBoxSecondType = new TextBox();
		this.textBoxModelIndex = new TextBox();
		this.textBoxIconIndex = new TextBox();
		this.textBoxItemRank = new TextBox();
		this.textBoxGrade = new TextBox();
		this.textBoxName = new TextBox();
		this.textBoxDescription = new TextBox();
		this.textBoxPrice = new TextBox();
		this.groupBoxSkill1 = new GroupBox();
		this.groupBoxSkill2 = new GroupBox();
		this.groupBoxSkill3 = new GroupBox();
		this.groupBoxSkill4 = new GroupBox();
		this.checkBoxBlockDrop = new CheckBox();
		this.checkBoxBlockTrade = new CheckBox();
		this.checkBoxBlockNPC = new CheckBox();
		this.checkBoxBlockStorage = new CheckBox();
		this.tabPageUnknown = new TabPage();

		// Initialize skill effect controls
		InitializeSkillControls();

		this.tabControl1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		this.tableLayoutPanel1.SuspendLayout();
		this.SuspendLayout();

		// listBoxSockets
		this.listBoxSockets.FormattingEnabled = true;
		this.listBoxSockets.Location = new Point(12, 12);
		this.listBoxSockets.Name = "listBoxSockets";
		this.listBoxSockets.Size = new Size(200, 500);
		this.listBoxSockets.TabIndex = 0;
		this.listBoxSockets.SelectedIndexChanged += new EventHandler(this.listBoxSockets_SelectedIndexChanged);

		// tabControl1
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPageUnknown);
		this.tabControl1.Location = new Point(230, 12);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new Size(800, 600);
		this.tabControl1.TabIndex = 1;

		// tabPage1
		this.tabPage1.Controls.Add(this.tableLayoutPanel1);
		this.tabPage1.Controls.Add(this.groupBoxSkill1);
		this.tabPage1.Controls.Add(this.groupBoxSkill2);
		this.tabPage1.Controls.Add(this.groupBoxSkill3);
		this.tabPage1.Controls.Add(this.groupBoxSkill4);
		this.tabPage1.Controls.Add(this.checkBoxBlockDrop);
		this.tabPage1.Controls.Add(this.checkBoxBlockTrade);
		this.tabPage1.Controls.Add(this.checkBoxBlockNPC);
		this.tabPage1.Controls.Add(this.checkBoxBlockStorage);
		this.tabPage1.Location = new Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new Padding(3);
		this.tabPage1.Size = new Size(792, 574);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "Socket Properties";
		this.tabPage1.UseVisualStyleBackColor = true;

		// tableLayoutPanel1
		this.tableLayoutPanel1.ColumnCount = 2;
		this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
		this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
		this.tableLayoutPanel1.Location = new Point(6, 6);
		this.tableLayoutPanel1.Name = "tableLayoutPanel1";
		this.tableLayoutPanel1.RowCount = 10;
		this.tableLayoutPanel1.Size = new Size(400, 300);
		this.tableLayoutPanel1.TabIndex = 0;

		// Add controls to table layout panel
		AddControlsToTableLayout();

		// Setup skill group boxes
		SetupSkillGroupBoxes();

		// Setup block checkboxes
		SetupBlockCheckBoxes();

		// tabPageUnknown
		this.tabPageUnknown.Location = new Point(4, 22);
		this.tabPageUnknown.Name = "tabPageUnknown";
		this.tabPageUnknown.Padding = new Padding(3);
		this.tabPageUnknown.Size = new Size(792, 574);
		this.tabPageUnknown.TabIndex = 1;
		this.tabPageUnknown.Text = "Unknown Data";
		this.tabPageUnknown.UseVisualStyleBackColor = true;

		// Form
		this.AutoScaleDimensions = new SizeF(6F, 13F);
		this.AutoScaleMode = AutoScaleMode.Font;
		this.ClientSize = new Size(1050, 650);
		this.Controls.Add(this.listBoxSockets);
		this.Controls.Add(this.tabControl1);
		this.Name = "FormSocket";
		this.Text = "Socket Editor";
		this.tabControl1.ResumeLayout(false);
		this.tabPage1.ResumeLayout(false);
		this.tableLayoutPanel1.ResumeLayout(false);
		this.ResumeLayout(false);
	}

	private void InitializeSkillControls()
	{
		// Skill 1 controls
		this.labelSkillID1 = new Label();
		this.labelSkillDesc1 = new Label();
		this.labelType1 = new Label();
		this.labelValue1 = new Label();
		this.labelProb1 = new Label();
		this.textBoxSkillID1 = new TextBox();
		this.textBoxSkillDesc1 = new TextBox();
		this.textBoxType1 = new TextBox();
		this.textBoxValue1 = new TextBox();
		this.textBoxProb1 = new TextBox();

		// Skill 2 controls
		this.labelSkillID2 = new Label();
		this.labelSkillDesc2 = new Label();
		this.labelType2 = new Label();
		this.labelValue2 = new Label();
		this.labelProb2 = new Label();
		this.textBoxSkillID2 = new TextBox();
		this.textBoxSkillDesc2 = new TextBox();
		this.textBoxType2 = new TextBox();
		this.textBoxValue2 = new TextBox();
		this.textBoxProb2 = new TextBox();

		// Skill 3 controls
		this.labelSkillID3 = new Label();
		this.labelSkillDesc3 = new Label();
		this.labelType3 = new Label();
		this.labelValue3 = new Label();
		this.labelProb3 = new Label();
		this.textBoxSkillID3 = new TextBox();
		this.textBoxSkillDesc3 = new TextBox();
		this.textBoxType3 = new TextBox();
		this.textBoxValue3 = new TextBox();
		this.textBoxProb3 = new TextBox();

		// Skill 4 controls
		this.labelSkillID4 = new Label();
		this.labelSkillDesc4 = new Label();
		this.labelType4 = new Label();
		this.labelValue4 = new Label();
		this.labelProb4 = new Label();
		this.textBoxSkillID4 = new TextBox();
		this.textBoxSkillDesc4 = new TextBox();
		this.textBoxType4 = new TextBox();
		this.textBoxValue4 = new TextBox();
		this.textBoxProb4 = new TextBox();
	}

	private void AddControlsToTableLayout()
	{
		// Setup labels
		this.label1.Text = "ID:";
		this.label2.Text = "Type:";
		this.label3.Text = "Second Type:";
		this.label4.Text = "Model Index:";
		this.label5.Text = "Icon Index:";
		this.label6.Text = "Item Rank:";
		this.label7.Text = "Grade:";
		this.label8.Text = "Name:";
		this.label9.Text = "Description:";
		this.label10.Text = "Price:";

		// Add controls to table layout
		this.tableLayoutPanel1.Controls.Add(this.label1, 0, 0);
		this.tableLayoutPanel1.Controls.Add(this.textBoxID, 1, 0);
		this.tableLayoutPanel1.Controls.Add(this.label2, 0, 1);
		this.tableLayoutPanel1.Controls.Add(this.textBoxType, 1, 1);
		this.tableLayoutPanel1.Controls.Add(this.label3, 0, 2);
		this.tableLayoutPanel1.Controls.Add(this.textBoxSecondType, 1, 2);
		this.tableLayoutPanel1.Controls.Add(this.label4, 0, 3);
		this.tableLayoutPanel1.Controls.Add(this.textBoxModelIndex, 1, 3);
		this.tableLayoutPanel1.Controls.Add(this.label5, 0, 4);
		this.tableLayoutPanel1.Controls.Add(this.textBoxIconIndex, 1, 4);
		this.tableLayoutPanel1.Controls.Add(this.label6, 0, 5);
		this.tableLayoutPanel1.Controls.Add(this.textBoxItemRank, 1, 5);
		this.tableLayoutPanel1.Controls.Add(this.label7, 0, 6);
		this.tableLayoutPanel1.Controls.Add(this.textBoxGrade, 1, 6);
		this.tableLayoutPanel1.Controls.Add(this.label8, 0, 7);
		this.tableLayoutPanel1.Controls.Add(this.textBoxName, 1, 7);
		this.tableLayoutPanel1.Controls.Add(this.label9, 0, 8);
		this.tableLayoutPanel1.Controls.Add(this.textBoxDescription, 1, 8);
		this.tableLayoutPanel1.Controls.Add(this.label10, 0, 9);
		this.tableLayoutPanel1.Controls.Add(this.textBoxPrice, 1, 9);

		// Setup text changed events
		this.textBoxID.TextChanged += (s, e) => SaveSocketData();
		this.textBoxType.TextChanged += (s, e) => SaveSocketData();
		this.textBoxSecondType.TextChanged += (s, e) => SaveSocketData();
		this.textBoxModelIndex.TextChanged += (s, e) => SaveSocketData();
		this.textBoxIconIndex.TextChanged += (s, e) => SaveSocketData();
		this.textBoxItemRank.TextChanged += (s, e) => SaveSocketData();
		this.textBoxGrade.TextChanged += (s, e) => SaveSocketData();
		this.textBoxPrice.TextChanged += (s, e) => SaveSocketData();
	}

	private void SetupSkillGroupBoxes()
	{
		// Skill Group Box 1
		this.groupBoxSkill1.Text = "Skill Effect 1";
		this.groupBoxSkill1.Location = new Point(420, 6);
		this.groupBoxSkill1.Size = new Size(180, 140);
		SetupSkillGroupBox(this.groupBoxSkill1, 1);

		// Skill Group Box 2
		this.groupBoxSkill2.Text = "Skill Effect 2";
		this.groupBoxSkill2.Location = new Point(610, 6);
		this.groupBoxSkill2.Size = new Size(180, 140);
		SetupSkillGroupBox(this.groupBoxSkill2, 2);

		// Skill Group Box 3
		this.groupBoxSkill3.Text = "Skill Effect 3";
		this.groupBoxSkill3.Location = new Point(420, 160);
		this.groupBoxSkill3.Size = new Size(180, 140);
		SetupSkillGroupBox(this.groupBoxSkill3, 3);

		// Skill Group Box 4
		this.groupBoxSkill4.Text = "Skill Effect 4";
		this.groupBoxSkill4.Location = new Point(610, 160);
		this.groupBoxSkill4.Size = new Size(180, 140);
		SetupSkillGroupBox(this.groupBoxSkill4, 4);
	}

	private void SetupSkillGroupBox(GroupBox groupBox, int skillNumber)
	{
		Label labelSkillID, labelSkillDesc, labelType, labelValue, labelProb;
		TextBox textBoxSkillID, textBoxSkillDesc, textBoxType, textBoxValue, textBoxProb;

		switch (skillNumber)
		{
			case 1:
				labelSkillID = this.labelSkillID1; textBoxSkillID = this.textBoxSkillID1;
				labelSkillDesc = this.labelSkillDesc1; textBoxSkillDesc = this.textBoxSkillDesc1;
				labelType = this.labelType1; textBoxType = this.textBoxType1;
				labelValue = this.labelValue1; textBoxValue = this.textBoxValue1;
				labelProb = this.labelProb1; textBoxProb = this.textBoxProb1;
				break;
			case 2:
				labelSkillID = this.labelSkillID2; textBoxSkillID = this.textBoxSkillID2;
				labelSkillDesc = this.labelSkillDesc2; textBoxSkillDesc = this.textBoxSkillDesc2;
				labelType = this.labelType2; textBoxType = this.textBoxType2;
				labelValue = this.labelValue2; textBoxValue = this.textBoxValue2;
				labelProb = this.labelProb2; textBoxProb = this.textBoxProb2;
				break;
			case 3:
				labelSkillID = this.labelSkillID3; textBoxSkillID = this.textBoxSkillID3;
				labelSkillDesc = this.labelSkillDesc3; textBoxSkillDesc = this.textBoxSkillDesc3;
				labelType = this.labelType3; textBoxType = this.textBoxType3;
				labelValue = this.labelValue3; textBoxValue = this.textBoxValue3;
				labelProb = this.labelProb3; textBoxProb = this.textBoxProb3;
				break;
			case 4:
				labelSkillID = this.labelSkillID4; textBoxSkillID = this.textBoxSkillID4;
				labelSkillDesc = this.labelSkillDesc4; textBoxSkillDesc = this.textBoxSkillDesc4;
				labelType = this.labelType4; textBoxType = this.textBoxType4;
				labelValue = this.labelValue4; textBoxValue = this.textBoxValue4;
				labelProb = this.labelProb4; textBoxProb = this.textBoxProb4;
				break;
			default:
				return;
		}

		// Setup labels
		labelSkillID.Text = "Skill ID:";
		labelSkillID.Location = new Point(6, 20);
		labelSkillID.Size = new Size(50, 13);

		labelSkillDesc.Text = "Desc ID:";
		labelSkillDesc.Location = new Point(6, 46);
		labelSkillDesc.Size = new Size(50, 13);

		labelType.Text = "Type:";
		labelType.Location = new Point(6, 72);
		labelType.Size = new Size(50, 13);

		labelValue.Text = "Value:";
		labelValue.Location = new Point(6, 98);
		labelValue.Size = new Size(50, 13);

		labelProb.Text = "Prob:";
		labelProb.Location = new Point(6, 124);
		labelProb.Size = new Size(50, 13);

		// Setup textboxes
		textBoxSkillID.Location = new Point(62, 17);
		textBoxSkillID.Size = new Size(100, 20);
		textBoxSkillID.TextChanged += (s, e) => SaveSocketData();

		textBoxSkillDesc.Location = new Point(62, 43);
		textBoxSkillDesc.Size = new Size(100, 20);
		textBoxSkillDesc.TextChanged += (s, e) => SaveSocketData();

		textBoxType.Location = new Point(62, 69);
		textBoxType.Size = new Size(100, 20);
		textBoxType.TextChanged += (s, e) => SaveSocketData();

		textBoxValue.Location = new Point(62, 95);
		textBoxValue.Size = new Size(100, 20);
		textBoxValue.TextChanged += (s, e) => SaveSocketData();

		textBoxProb.Location = new Point(62, 121);
		textBoxProb.Size = new Size(100, 20);
		textBoxProb.TextChanged += (s, e) => SaveSocketData();

		// Add controls to group box
		groupBox.Controls.Add(labelSkillID);
		groupBox.Controls.Add(textBoxSkillID);
		groupBox.Controls.Add(labelSkillDesc);
		groupBox.Controls.Add(textBoxSkillDesc);
		groupBox.Controls.Add(labelType);
		groupBox.Controls.Add(textBoxType);
		groupBox.Controls.Add(labelValue);
		groupBox.Controls.Add(textBoxValue);
		groupBox.Controls.Add(labelProb);
		groupBox.Controls.Add(textBoxProb);
	}

	private void SetupBlockCheckBoxes()
	{
		this.checkBoxBlockDrop.Text = "Block Drop";
		this.checkBoxBlockDrop.Location = new Point(6, 320);
		this.checkBoxBlockDrop.CheckedChanged += (s, e) => SaveSocketData();

		this.checkBoxBlockTrade.Text = "Block Trade";
		this.checkBoxBlockTrade.Location = new Point(6, 346);
		this.checkBoxBlockTrade.CheckedChanged += (s, e) => SaveSocketData();

		this.checkBoxBlockNPC.Text = "Block NPC";
		this.checkBoxBlockNPC.Location = new Point(6, 372);
		this.checkBoxBlockNPC.CheckedChanged += (s, e) => SaveSocketData();

		this.checkBoxBlockStorage.Text = "Block Storage";
		this.checkBoxBlockStorage.Location = new Point(6, 398);
		this.checkBoxBlockStorage.CheckedChanged += (s, e) => SaveSocketData();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}
}
