using System.IO;

namespace ItemTableReader;

internal class ItemSocket : ItemBase
{
	private static uint XSD_START_INDEX = 60000u;

	private uint _xsdName;
	private uint _xsdInfo;

	public uint xsdName;
	public uint xsdInfo;

	public static Map<ItemSocket> Sockets = new Map<ItemSocket>();

	// SOCKETPROPERTY structure fields
	public sbyte objType3;
	public short skillEffectID1;
	public ushort skillDescID1;
	public sbyte type1;
	public short value1;
	public sbyte prob1;
	public sbyte apType1;
	public short apPer1;
	public short apCount1;

	public short skillEffectID2;
	public ushort skillDescID2;
	public sbyte type2;
	public short value2;
	public sbyte prob2;
	public sbyte apType2;
	public short apPer2;
	public short apCount2;

	public short skillEffectID3;
	public ushort skillDescID3;
	public sbyte type3;
	public short value3;
	public sbyte prob3;
	public sbyte apType3;
	public short apPer3;
	public short apCount3;

	public short skillEffectID4;
	public ushort skillDescID4;
	public sbyte type4;
	public short value4;
	public sbyte prob4;
	public sbyte apType4;
	public short apPer4;
	public short apCount4;

	public int iUnk1; // clan point 1 ?
	public int iUnk2; // clan point 2 ?

	public short sUnk1;
	public int iUnk3;
	public short sUnk2;

	public sbyte dump;
	public sbyte userTrade;
	public sbyte npcTrade;

	public sbyte cUnk1;
	public short sUnk3;
	public short sUnk4;
	public sbyte cUnk2;
	public short sUnk5;
	public short sUnk6;
	public short sUnk7;

	public static uint Size => 112u;

	public void updateXsdName()
	{
		_xsdName = xsdName + XSD_START_INDEX;
	}

	public void updateXsdInfo()
	{
		_xsdInfo = xsdInfo + XSD_START_INDEX;
	}

	public override void Load(Stream s)
	{
		// Load XSD data if not already loaded
		if (XsdManager.Maps["SocketName"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["SocketName"], XsdManager.TableNames["SocketName"]);
		}
		if (XsdManager.Maps["SocketInfo"].Count == 0)
		{
			XsdManager.GetDataTo(XsdManager.Maps["SocketInfo"], XsdManager.TableNames["SocketInfo"]);
		}

		BinaryReader binaryReader = new BinaryReader(s);

		// Read basic item properties (objType, objType2, objID)
		base.Type = binaryReader.ReadSByte();           // objType
		base.SecondType = binaryReader.ReadSByte();     // objType2
		base.ID = binaryReader.ReadInt16();             // objID

		// Read model and icon info
		base.ModelIndex = binaryReader.ReadInt16();     // modelIndex
		base.IconIndex = binaryReader.ReadInt16();      // iconNo
		base.ItemRank = binaryReader.ReadUInt16();      // itemRank
		base.Grade = binaryReader.ReadByte();           // grade

		// Read XSD references
		uint descriptionID = binaryReader.ReadUInt32(); // descriptionID
		uint nameID = binaryReader.ReadUInt32();        // nameID

		// Convert XSD IDs (subtract 60000 as per comment)
		_xsdInfo = descriptionID;
		_xsdName = nameID;
		xsdInfo = descriptionID - XSD_START_INDEX;
		xsdName = nameID - XSD_START_INDEX;

		// Load name and description from XSD
		int key = (int)xsdName;
		if (XsdManager.Maps["SocketName"].ContainsKey(key))
		{
			base.Name = XsdManager.Maps["SocketName"][key];
		}

		key = (int)xsdInfo;
		if (XsdManager.Maps["SocketInfo"].ContainsKey(key))
		{
			base.Description = XsdManager.Maps["SocketInfo"][key];
		}

		// Continue reading SOCKETPROPERTY fields
		objType3 = binaryReader.ReadSByte();            // objType3

		// Skill Effect 1
		skillEffectID1 = binaryReader.ReadInt16();      // skillEffectID1
		skillDescID1 = binaryReader.ReadUInt16();       // skillDescID1
		type1 = binaryReader.ReadSByte();               // type1
		value1 = binaryReader.ReadInt16();              // value1
		prob1 = binaryReader.ReadSByte();               // prob1
		apType1 = binaryReader.ReadSByte();             // apType1
		apPer1 = binaryReader.ReadInt16();              // apPer1
		apCount1 = binaryReader.ReadInt16();            // apCount1

		// Skill Effect 2
		skillEffectID2 = binaryReader.ReadInt16();      // skillEffectID2
		skillDescID2 = binaryReader.ReadUInt16();       // skillDescID2
		type2 = binaryReader.ReadSByte();               // type2
		value2 = binaryReader.ReadInt16();              // value2
		prob2 = binaryReader.ReadSByte();               // prob2
		apType2 = binaryReader.ReadSByte();             // apType2
		apPer2 = binaryReader.ReadInt16();              // apPer2
		apCount2 = binaryReader.ReadInt16();            // apCount2

		// Skill Effect 3
		skillEffectID3 = binaryReader.ReadInt16();      // skillEffectID3
		skillDescID3 = binaryReader.ReadUInt16();       // skillDescID3
		type3 = binaryReader.ReadSByte();               // type3
		value3 = binaryReader.ReadInt16();              // value3
		prob3 = binaryReader.ReadSByte();               // prob3
		apType3 = binaryReader.ReadSByte();             // apType3
		apPer3 = binaryReader.ReadInt16();              // apPer3
		apCount3 = binaryReader.ReadInt16();            // apCount3

		// Skill Effect 4
		skillEffectID4 = binaryReader.ReadInt16();      // skillEffectID4
		skillDescID4 = binaryReader.ReadUInt16();       // skillDescID4
		type4 = binaryReader.ReadSByte();               // type4
		value4 = binaryReader.ReadInt16();              // value4
		prob4 = binaryReader.ReadSByte();               // prob4
		apType4 = binaryReader.ReadSByte();             // apType4
		apPer4 = binaryReader.ReadInt16();              // apPer4
		apCount4 = binaryReader.ReadInt16();            // apCount4

		// Quality and clan info
		base.Quality = binaryReader.ReadByte();         // quality
		base.Quality2 = binaryReader.ReadByte();        // quality2
		base.ApplyClan = binaryReader.ReadSByte();      // applyClan

		// Unknown clan points
		iUnk1 = binaryReader.ReadInt32();               // iUnk1 (clan point 1?)
		iUnk2 = binaryReader.ReadInt32();               // iUnk2 (clan point 2?)

		// Price
		base.Price = binaryReader.ReadUInt32();         // price

		// Unknown fields
		sUnk1 = binaryReader.ReadInt16();               // sUnk1
		iUnk3 = binaryReader.ReadInt32();               // iUnk3
		sUnk2 = binaryReader.ReadInt16();               // sUnk2

		// Trade and drop settings
		dump = binaryReader.ReadSByte();                // dump
		userTrade = binaryReader.ReadSByte();           // userTrade
		npcTrade = binaryReader.ReadSByte();            // npcTrade
		base.Fame = binaryReader.ReadByte();            // fame
		base.CashCheck = binaryReader.ReadSByte();      // cashCheck

		// More unknown fields
		cUnk1 = binaryReader.ReadSByte();               // cUnk1
		sUnk3 = binaryReader.ReadInt16();               // sUnk3
		sUnk4 = binaryReader.ReadInt16();               // sUnk4
		cUnk2 = binaryReader.ReadSByte();               // cUnk2
		sUnk5 = binaryReader.ReadInt16();               // sUnk5
		sUnk6 = binaryReader.ReadInt16();               // sUnk6
		sUnk7 = binaryReader.ReadInt16();               // sUnk7
	}

	public override void Save(Stream s)
	{
		// Update XSD references before saving
		updateXsdName();
		updateXsdInfo();

		BinaryWriter binaryWriter = new BinaryWriter(s);

		// Write basic item properties (objType, objType2, objID)
		binaryWriter.Write(base.Type);                  // objType
		binaryWriter.Write(base.SecondType);            // objType2
		binaryWriter.Write(base.ID);                    // objID

		// Write model and icon info
		binaryWriter.Write(base.ModelIndex);            // modelIndex
		binaryWriter.Write(base.IconIndex);             // iconNo
		binaryWriter.Write(base.ItemRank);              // itemRank
		binaryWriter.Write(base.Grade);                 // grade

		// Write XSD references
		binaryWriter.Write(_xsdInfo);                   // descriptionID
		binaryWriter.Write(_xsdName);                   // nameID

		// Continue writing SOCKETPROPERTY fields
		binaryWriter.Write(objType3);                   // objType3

		// Skill Effect 1
		binaryWriter.Write(skillEffectID1);             // skillEffectID1
		binaryWriter.Write(skillDescID1);               // skillDescID1
		binaryWriter.Write(type1);                      // type1
		binaryWriter.Write(value1);                     // value1
		binaryWriter.Write(prob1);                      // prob1
		binaryWriter.Write(apType1);                    // apType1
		binaryWriter.Write(apPer1);                     // apPer1
		binaryWriter.Write(apCount1);                   // apCount1

		// Skill Effect 2
		binaryWriter.Write(skillEffectID2);             // skillEffectID2
		binaryWriter.Write(skillDescID2);               // skillDescID2
		binaryWriter.Write(type2);                      // type2
		binaryWriter.Write(value2);                     // value2
		binaryWriter.Write(prob2);                      // prob2
		binaryWriter.Write(apType2);                    // apType2
		binaryWriter.Write(apPer2);                     // apPer2
		binaryWriter.Write(apCount2);                   // apCount2

		// Skill Effect 3
		binaryWriter.Write(skillEffectID3);             // skillEffectID3
		binaryWriter.Write(skillDescID3);               // skillDescID3
		binaryWriter.Write(type3);                      // type3
		binaryWriter.Write(value3);                     // value3
		binaryWriter.Write(prob3);                      // prob3
		binaryWriter.Write(apType3);                    // apType3
		binaryWriter.Write(apPer3);                     // apPer3
		binaryWriter.Write(apCount3);                   // apCount3

		// Skill Effect 4
		binaryWriter.Write(skillEffectID4);             // skillEffectID4
		binaryWriter.Write(skillDescID4);               // skillDescID4
		binaryWriter.Write(type4);                      // type4
		binaryWriter.Write(value4);                     // value4
		binaryWriter.Write(prob4);                      // prob4
		binaryWriter.Write(apType4);                    // apType4
		binaryWriter.Write(apPer4);                     // apPer4
		binaryWriter.Write(apCount4);                   // apCount4

		// Quality and clan info
		binaryWriter.Write(base.Quality);               // quality
		binaryWriter.Write(base.Quality2);              // quality2
		binaryWriter.Write(base.ApplyClan);             // applyClan

		// Unknown clan points
		binaryWriter.Write(iUnk1);                      // iUnk1 (clan point 1?)
		binaryWriter.Write(iUnk2);                      // iUnk2 (clan point 2?)

		// Price
		binaryWriter.Write(base.Price);                 // price

		// Unknown fields
		binaryWriter.Write(sUnk1);                      // sUnk1
		binaryWriter.Write(iUnk3);                      // iUnk3
		binaryWriter.Write(sUnk2);                      // sUnk2

		// Trade and drop settings
		binaryWriter.Write(dump);                       // dump
		binaryWriter.Write(userTrade);                  // userTrade
		binaryWriter.Write(npcTrade);                   // npcTrade
		binaryWriter.Write(base.Fame);                  // fame
		binaryWriter.Write(base.CashCheck);             // cashCheck

		// More unknown fields
		binaryWriter.Write(cUnk1);                      // cUnk1
		binaryWriter.Write(sUnk3);                      // sUnk3
		binaryWriter.Write(sUnk4);                      // sUnk4
		binaryWriter.Write(cUnk2);                      // cUnk2
		binaryWriter.Write(sUnk5);                      // sUnk5
		binaryWriter.Write(sUnk6);                      // sUnk6
		binaryWriter.Write(sUnk7);                      // sUnk7
	}
}
