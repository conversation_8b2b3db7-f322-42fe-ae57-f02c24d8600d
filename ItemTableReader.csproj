﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <AssemblyName>ItemTableReader</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>True</UseWindowsForms>
    <TargetFramework>net45</TargetFramework>
    <Prefer32Bit>True</Prefer32Bit>

  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>11.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Design" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
  </ItemGroup>
</Project>