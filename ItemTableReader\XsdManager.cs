using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using NineDragons.XStringDatabase;

namespace ItemTableReader;

internal static class XsdManager
{
	private static Map<string, Map> maps;

	private static Map<string, string> tables;

	private static Map mapEffectsName;

	private static Map mapEffectsInfo;

	private static Map mapWeaponNames;

	private static Map mapClothNames;

	private static Map mapClothInfo;

	private static Map mapNicknames;

	private static Map mapAddTo;

	private static Map mapWeaponInfo;

	public static Map mapelixirs;

	public static List<Xsd> xsd;

	private static byte[] keys;

	public static Map<string, Map> Maps => maps;

	public static Map<string, string> TableNames => tables;

	public static Map AddTos => mapAddTo;

	public static Map Nicknames => mapNicknames;

	public static Map WeaponNames => mapWeaponNames;

	public static Map WeaponInfo => mapWeaponInfo;

	public static Map ClothNames => mapClothNames;

	public static Map ClothInfo => mapClothInfo;

	public static Map EffectsName => mapEffectsName;

	public static Map EffectsInfo => mapEffectsInfo;

	public static Xsd XSD => xsd[0];

	static XsdManager()
	{
		maps = new Map<string, Map>();
		tables = new Map<string, string>();
		mapEffectsName = new Map();
		mapEffectsInfo = new Map();
		mapWeaponNames = new Map();
		mapClothNames = new Map();
		mapClothInfo = new Map();
		mapNicknames = new Map();
		mapAddTo = new Map();
		mapWeaponInfo = new Map();
		mapelixirs = new Map();
		xsd = new List<Xsd>();
		//keys = new byte[2] { 23, 8 };//XSD����17,08
        keys = new byte[2] { 149, 153 };//XSD����95,99
        tables["Nickname"] = "ItemTable_Nickname";
		tables["AddTo"] = "ItemTable_AddTo";
		tables["WeaponName"] = "ItemTable_SeedWeapon";
		tables["WeaponInfo"] = "ItemTable_SeedInfoWeapon";
		tables["ClothName"] = "ItemTable_SeedClothes";
		tables["ClothInfo"] = "ItemTable_SeedInfoClothes";
		tables["BookName"] = "ItemTable_SeedBook";
		tables["BookInfo"] = "ItemTable_SeedInfoBook";
		tables["PotionName"] = "ItemTable_SeedPotion";
		tables["PotionInfo"] = "ItemTable_SeedInfoPotion";
		tables["ConsumingName"] = "ItemTable_SeedConsuming";
		tables["ConsumingInfo"] = "ItemTable_SeedInfoConsuming";
		tables["ElixirName"] = "ItemTable_SeedElixir";
		tables["ElixirInfo"] = "ItemTable_SeedInfoElixir";
		tables["QuestName"] = "ItemTable_SeedQuest";
		tables["QuestInfo"] = "ItemTable_SeedInfoQuest";
		tables["AccessoryName"] = "ItemTable_SeedAccessory";
		tables["AccessoryInfo"] = "ItemTable_SeedInfoAccessory";
		tables["ResourceName"] = "ItemTable_SeedResource";
		tables["ResourceInfo"] = "ItemTable_SeedInfoResource";
		tables["LifeName"] = "ItemTable_SeedLife";
		tables["LifeInfo"] = "ItemTable_SeedInfoLife";
		tables["SocketName"] = "ItemTable_SeedSocket";
		tables["SocketInfo"] = "ItemTable_SeedInfoSocket";
		tables["BoxName"] = "ItemTable_SeedBox";
		tables["BoxInfo"] = "ItemTable_SeedInfoBox";
		tables["BoxkeyName"] = "ItemTable_SeedBoxkey";
		tables["BoxkeyInfo"] = "ItemTable_SeedInfoBoxkey";
		tables["EffectsName"] = "CharacterState_name";
		tables["EffectsInfo"] = "CharacterState_info";
		tables["Fame"] = "Fame_Infamy_Ref";
		foreach (string key in tables.Keys)
		{
			maps[key] = new Map();
			GetDataTo(maps[key], tables[key]);
		}
		FameTableFix();
	}

	public static string GetInfoFrom(string tablename, uint index)
	{
		if (Maps[tablename].Count == 0)
		{
			GetDataTo(Maps[tablename], TableNames[tablename]);
		}
		if (Maps[tablename].ContainsKey((int)index))
		{
			return Maps[tablename][(int)index];
		}
		return null;
	}

	private static void FameTableFix()
	{
		int[] array = maps["Fame"].Keys.ToArray();
		foreach (int key in array)
		{
			maps["Fame"][key] = maps["Fame"][key].Replace('_', ' ');
		}
		maps["Fame"].Add(0, "None");
		maps["Fame"].Add(99, "Good Karma");
		maps["Fame"].Add(199, "Bad Karma");
	}

	public static void GetDataTo(Map map, string tableName)
	{
		if (xsd.Count == 0)
		{
			OpenXsd(0);
		}
		Section section = null;
		foreach (Section section2 in XSD.sectionCollection.Sections)
		{
			if (section2.UnicodeName.Contains(tableName))
			{
				section = section2;
				break;
			}
		}
		if (section == null)
		{
			return;
		}
		foreach (XString row in section.XStrings.Rows)
		{
			if (!map.ContainsKey(row.ResourceIndex))
			{
				map.Add(row.ResourceIndex, row.UnicodeName());
			}
		}
	}

	internal static bool OpenXsd(int xsdIndex)
	{
		string text = OpenXsdDialog();
		if (text == string.Empty)
		{
			return false;
		}
		if (xsdIndex == 0)
		{
			xsd.Clear();
		}
		if (xsdIndex > 0 && xsd[0] == null)
		{
			MessageBox.Show("Cannot open additional XSDs. Try loading an XSD.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
		}
		xsd.Insert(xsdIndex, new XsdFile(text, keys));
		xsd[xsdIndex].load();
		return true;
	}

	private static string OpenXsdDialog()
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Filter = "XSD files (*.xsd)|*.xsd|All files (*.*)|*.*";
		openFileDialog.Title = "Select XSD Database File";
		openFileDialog.InitialDirectory = Application.StartupPath;

		if (openFileDialog.ShowDialog() == DialogResult.OK)
		{
			return openFileDialog.FileName;
		}
		return string.Empty;
	}
}
