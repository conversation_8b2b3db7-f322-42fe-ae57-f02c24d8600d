using System;
using System.IO;
using ItemTableReader;

class TestSocket
{
    static void Main()
    {
        Console.WriteLine("Testing Socket functionality...");
        
        // Create a test socket
        ItemSocket testSocket = new ItemSocket();
        testSocket.ID = 1001;
        testSocket.Type = 10;
        testSocket.SecondType = 1;
        testSocket.ModelIndex = 100;
        testSocket.IconIndex = 50;
        testSocket.ItemRank = 1;
        testSocket.Grade = 1;
        testSocket.Name = "Test Socket";
        testSocket.Description = "Test Socket Description";
        testSocket.Price = 1000;
        
        // Set skill effects
        testSocket.skillEffectID1 = 101;
        testSocket.skillDescID1 = 201;
        testSocket.type1 = 1;
        testSocket.value1 = 10;
        testSocket.prob1 = 50;
        
        testSocket.skillEffectID2 = 102;
        testSocket.skillDescID2 = 202;
        testSocket.type2 = 2;
        testSocket.value2 = 20;
        testSocket.prob2 = 60;
        
        // Test saving to memory stream
        using (MemoryStream ms = new MemoryStream())
        {
            testSocket.Save(ms);
            Console.WriteLine($"Socket saved successfully. Size: {ms.Length} bytes");
            
            // Test loading from memory stream
            ms.Position = 0;
            ItemSocket loadedSocket = new ItemSocket();
            loadedSocket.Load(ms);
            
            Console.WriteLine($"Socket loaded successfully.");
            Console.WriteLine($"ID: {loadedSocket.ID}");
            Console.WriteLine($"Name: {loadedSocket.Name}");
            Console.WriteLine($"Description: {loadedSocket.Description}");
            Console.WriteLine($"Price: {loadedSocket.Price}");
            Console.WriteLine($"Skill Effect 1 ID: {loadedSocket.skillEffectID1}");
            Console.WriteLine($"Skill Effect 1 Value: {loadedSocket.value1}");
            Console.WriteLine($"Skill Effect 2 ID: {loadedSocket.skillEffectID2}");
            Console.WriteLine($"Skill Effect 2 Value: {loadedSocket.value2}");
        }
        
        Console.WriteLine("Socket functionality test completed successfully!");
        Console.ReadKey();
    }
}
